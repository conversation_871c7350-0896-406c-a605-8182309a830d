import type {
    ScheduledEvent,
    ExecutionContext,
    D1PreparedStatement,
    D1Result
} from '@cloudflare/workers-types';

interface Env {
    APPS_STORE_ID_TMP: KVNamespace;
    DB: D1Database;
    API_KEY: string;
    MANUAL_TRIGGER_KEY: string;
    LARK_WEBHOOK_URL: string;
}

interface AppConfig {
    package_name: string;
    app_name: string;
}

interface PlayStoreResponse {
    status: 'available' | 'unavailable';
    title?: string;
    appId?: string;
    developer?: {
        devId?: string;
    };
    developerId?: string;
    developerEmail?: string;
    developerWebsite?: string;
    developerLegalName?: string;
    developerLegalEmail?: string;
    developerLegalAddress?: string;
    developerLegalPhoneNumber?: string;
    developerInternalID?: string;
    playstoreUrl?: string;
    description?: string;
    installs?: string;
    minInstalls?: number;
    maxInstalls?: number;
	released?: string;
}

interface AppStatusRecord {
    package_name: string;
    title?: string;
    appId?: string;
    devId?: string;
    developerId?: string;
    developerEmail?: string;
    developerWebsite?: string;
    developerLegalName?: string;
    developerLegalEmail?: string;
    developerLegalAddress?: string;
    developerLegalPhoneNumber?: string;
    developerInternalID?: string;
    isAlive: number;
    lastCheck: string;
    belongUs: number;
    downTime?: string | null;
    released?: string;
}

interface AppStatusRecord {
    package_name: string;
    title?: string;
    appId?: string;
    devId?: string;
    developerId?: string;
    developerEmail?: string;
    developerWebsite?: string;
    developerLegalName?: string;
    developerLegalEmail?: string;
    developerLegalAddress?: string;
    developerLegalPhoneNumber?: string;
    developerInternalID?: string;
    isAlive: number;
    lastCheck: string;
    belongUs: number;
    downTime?: string | null;
    released?: string;
}

export default {
    async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
        const url = new URL(request.url);

        // 添加健康检查端点
        if (url.pathname === '/health') {
            try {
                const result = await env.DB.prepare("SELECT 1 AS status").first();
                return Response.json({ status: 'ok', db: result?.status === 1 });
            } catch (err) {
                const errorMessage = err instanceof Error ? err.message : 'Unknown error';
                return Response.json({ status: 'error', message: errorMessage }, { status: 500 });
            }
        }

        if (url.pathname === '/trigger') {
            const authHeader = request.headers.get('Authorization');
            if (authHeader !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            ctx.waitUntil(executeMonitor(env));
            return new Response('监测任务已启动');
        }

        // 新增管理接口
        if (url.pathname === '/manage') {
            const auth = request.headers.get('Authorization');
            if (auth !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            // 处理应用列表管理
            if (request.method === 'GET') {
                const apps = await getAllTrackedApps(env);
                return Response.json(apps);
            }

            if (request.method === 'POST') {
                const body = await request.json<AppConfig>();
                await addTrackedApp(env, body);
                return new Response('应用已添加');
            }

            if (request.method === 'DELETE') {
                const { package_name } = await request.json<{ package_name: string }>();
                await removeTrackedApp(env, package_name);
                return new Response('应用已移除');
            }
        }

        // 添加测试端点
        if (url.pathname === '/test-lark') {
            const authHeader = request.headers.get('Authorization');
            if (authHeader !== `Bearer ${env.MANUAL_TRIGGER_KEY}`) {
                return new Response('Unauthorized', { status: 401 });
            }

            try {
                await sendLarkAlert(env,
                    { package_name: 'test.package', app_name: 'Test App' },
                    '这是一条测试消息\n包含多行内容\n用于测试消息发送');
                return new Response('Test message sent successfully');
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                return new Response(`Failed to send test message: ${errorMessage}`, { status: 500 });
            }
        }

        return new Response(`管理端点:
GET /manage - 获取跟踪应用列表
POST /manage - 添加应用 {package_name, app_name}
DELETE /manage - 移除应用 {package_name}`);
    },

    async scheduled(_event: ScheduledEvent, env: Env, ctx: ExecutionContext): Promise<void> {
        ctx.waitUntil(executeMonitor(env));
    }
};

// 安全执行D1数据库操作（优化版）
async function d1SafeRun(query: D1PreparedStatement, logErrors: boolean = true): Promise<D1Result> {
    try {
        const result = await query.run();

        if (!result.success) {
            throw new Error(result.error || 'D1 操作失败');
        }

        return result;
    } catch (err) {
        if (logErrors) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown error';
            console.error('数据库操作失败:', errorMessage);
        }
        throw err;
    }
}

// 安全截断字段到指定长度
function truncateField(value: string | null | undefined, maxLength: number): string | null {
    return value ? value.substring(0, maxLength) : null;
}

// 应用列表管理函数（优化版）
async function getAllTrackedApps(env: Env): Promise<AppConfig[]> {
    const list = await env.APPS_STORE_ID_TMP.list();
    const apps: AppConfig[] = [];

    for (const key of list.keys) {
        const app = await env.APPS_STORE_ID_TMP.get<AppConfig>(key.name, 'json');
        if (app) apps.push(app);
    }

    return apps;
}

async function addTrackedApp(env: Env, app: AppConfig) {
    // 存储应用到APPS_STORE
    await env.APPS_STORE_ID_TMP.put(app.package_name, JSON.stringify(app));

    const now = new Date().toISOString();
    try {
        const details = await checkPlayStore(env.API_KEY, app.package_name);

        // 确定初始状态
        const isAlive = details.status === 'available';

        // 准备要插入的数据字段，使用 truncateField 确保长度
        const insertData = {
            package_name: app.package_name,
            title: truncateField(details.title, 255),
            appId: truncateField(details.appId, 100),
            devId: truncateField(details.developer?.devId, 100),
            developerId: truncateField(details.developerId, 100),
            developerEmail: truncateField(details.developerEmail, 255),
            developerWebsite: truncateField(details.developerWebsite, 255),
            developerLegalName: truncateField(details.developerLegalName, 255),
            developerLegalEmail: truncateField(details.developerLegalEmail, 255),
            developerLegalAddress: truncateField(details.developerLegalAddress, 500),
            developerLegalPhoneNumber: truncateField(details.developerLegalPhoneNumber, 50),
            developerInternalID: truncateField(details.developerInternalID, 100),
            isAlive: isAlive ? 1 : 0,
            lastCheck: now,
            downTime: isAlive ? null : now,
            released: truncateField(details.released, 50)
        };

        // 构建SQL插入语句（为新应用设置belongUs默认值0）
        const insertQuery = env.DB.prepare(`
            INSERT INTO app_status (
                package_name, title, appId, devId, developerId,
                developerEmail, developerWebsite, developerLegalName,
                developerLegalEmail, developerLegalAddress, developerLegalPhoneNumber,
                developerInternalID, isAlive, lastCheck, belongUs, downTime, released
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
            insertData.package_name,
            insertData.title,
            insertData.appId,
            insertData.devId,
            insertData.developerId,
            insertData.developerEmail,
            insertData.developerWebsite,
            insertData.developerLegalName,
            insertData.developerLegalEmail,
            insertData.developerLegalAddress,
            insertData.developerLegalPhoneNumber,
            insertData.developerInternalID,
            insertData.isAlive,
            insertData.lastCheck,
            0, // belongUs 默认值
            insertData.downTime,
			insertData.released
        );

        await d1SafeRun(insertQuery, false);
        console.log(`应用 ${app.app_name} 添加成功`);

    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        console.error(`添加应用 ${app.app_name} 失败: ${errorMessage}`);

        // 回退插入基本记录
        try {
            const fallbackInsert = env.DB.prepare(`
                INSERT INTO app_status (package_name, isAlive, lastCheck, belongUs)
                VALUES (?, ?, ?, ?)
            `).bind(app.package_name, 1, now, 0);

            await d1SafeRun(fallbackInsert, false);
        } catch (fallbackErr) {
            console.error(`回退插入失败: ${app.package_name}`);
        }
    }
}

async function removeTrackedApp(env: Env, package_name: string) {
    await env.APPS_STORE_ID_TMP.delete(package_name);
}

// 全局监控锁，防止重叠执行
let isMonitorRunning = false;

// 监控核心逻辑（优化版）
async function executeMonitor(env: Env) {
    // 检查是否已有监控任务在运行
    if (isMonitorRunning) {
        console.log('监控任务已在运行中，跳过本次执行');
        return;
    }

    isMonitorRunning = true;
    const startTime = Date.now();

    try {
        const apps = await getAllTrackedApps(env);
        console.log(`开始监控 ${apps.length} 个应用`);

        let successCount = 0;
        let errorCount = 0;

        for (const app of apps) {
            try {
                await checkAppWithBackoff(app, env);
                successCount++;
                // 适当间隔，确保单次监控在4分钟内完成 (120个应用约需4分钟)
                await new Promise(resolve => setTimeout(resolve, 2000));
            } catch (err) {
                errorCount++;
                console.error(`检查 ${app.app_name} 失败:`, err instanceof Error ? err.message : err);
            }
        }

        const duration = Math.round((Date.now() - startTime) / 1000);
        console.log(`监控完成: 成功 ${successCount}, 失败 ${errorCount}, 耗时 ${duration}秒`);
    } finally {
        isMonitorRunning = false;
    }
}

async function checkAppWithBackoff(app: AppConfig, env: Env) {
    const now = new Date().toISOString();
    let details: PlayStoreResponse;

    try {
        // 获取应用详情
        details = await checkPlayStore(env.API_KEY, app.package_name);
        const currentStatus = details.status === 'available';

        // 查询当前状态
        const query = env.DB.prepare("SELECT isAlive, downTime FROM app_status WHERE package_name = ?").bind(app.package_name);
        const result = await d1SafeRun(query, false);

        const prevRecord = result.results[0] as { isAlive: number; downTime?: string | null } | null;
        const prevAlive = prevRecord ? Boolean(prevRecord.isAlive) : true;

        // 只在状态变化时输出日志
        if (prevAlive !== currentStatus) {
            console.log(`${app.app_name}: ${prevAlive ? '在架' : '下架'} -> ${currentStatus ? '在架' : '下架'}`);
        }

        // 确定下架时间
        let downTime = prevRecord?.downTime || null;
        if (prevAlive && !currentStatus) {
            downTime = now; // 刚下架
        } else if (!prevAlive && currentStatus) {
            downTime = null; // 重新上架
        }

        // 根据是否存在记录和应用状态选择更新策略
        let dbQuery;
        if (prevRecord) {
            // 存在记录，只更新必要字段
            if (currentStatus) {
                // 应用在架时，更新详细信息（避免覆盖下架时的数据）
                const data = {
                    title: truncateField(details.title, 255),
                    appId: truncateField(details.appId, 100),
                    devId: truncateField(details.developer?.devId, 100),
                    developerId: truncateField(details.developerId, 100),
                    developerEmail: truncateField(details.developerEmail, 255),
                    developerWebsite: truncateField(details.developerWebsite, 255),
                    developerLegalName: truncateField(details.developerLegalName, 255),
                    developerLegalEmail: truncateField(details.developerLegalEmail, 255),
                    developerLegalAddress: truncateField(details.developerLegalAddress, 500),
                    developerLegalPhoneNumber: truncateField(details.developerLegalPhoneNumber, 50),
                    developerInternalID: truncateField(details.developerInternalID, 100),
                    released: truncateField(details.released, 50)
                };

                dbQuery = env.DB.prepare(`
                    UPDATE app_status SET
                        title = ?, appId = ?, devId = ?, developerId = ?,
                        developerEmail = ?, developerWebsite = ?, developerLegalName = ?,
                        developerLegalEmail = ?, developerLegalAddress = ?, developerLegalPhoneNumber = ?,
                        developerInternalID = ?, released = ?,
                        isAlive = ?, lastCheck = ?, downTime = ?
                    WHERE package_name = ?
                `).bind(
                    data.title, data.appId, data.devId, data.developerId,
                    data.developerEmail, data.developerWebsite, data.developerLegalName,
                    data.developerLegalEmail, data.developerLegalAddress, data.developerLegalPhoneNumber,
                    data.developerInternalID, data.released,
                    1, now, downTime, app.package_name
                );
            } else {
                // 应用下架时，只更新状态相关字段，保留原有数据
                dbQuery = env.DB.prepare(`
                    UPDATE app_status SET
                        isAlive = ?, lastCheck = ?, downTime = ?
                    WHERE package_name = ?
                `).bind(0, now, downTime, app.package_name);
            }
        } else {
            // 不存在记录，执行插入
            if (currentStatus) {
                // 应用在架时插入完整数据
                const data = {
                    title: truncateField(details.title, 255),
                    appId: truncateField(details.appId, 100),
                    devId: truncateField(details.developer?.devId, 100),
                    developerId: truncateField(details.developerId, 100),
                    developerEmail: truncateField(details.developerEmail, 255),
                    developerWebsite: truncateField(details.developerWebsite, 255),
                    developerLegalName: truncateField(details.developerLegalName, 255),
                    developerLegalEmail: truncateField(details.developerLegalEmail, 255),
                    developerLegalAddress: truncateField(details.developerLegalAddress, 500),
                    developerLegalPhoneNumber: truncateField(details.developerLegalPhoneNumber, 50),
                    developerInternalID: truncateField(details.developerInternalID, 100),
                    released: truncateField(details.released, 50)
                };

                dbQuery = env.DB.prepare(`
                    INSERT INTO app_status (
                        package_name, title, appId, devId, developerId,
                        developerEmail, developerWebsite, developerLegalName,
                        developerLegalEmail, developerLegalAddress, developerLegalPhoneNumber,
                        developerInternalID, isAlive, lastCheck, belongUs, downTime, released
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `).bind(
                    app.package_name, data.title, data.appId, data.devId, data.developerId,
                    data.developerEmail, data.developerWebsite, data.developerLegalName,
                    data.developerLegalEmail, data.developerLegalAddress, data.developerLegalPhoneNumber,
                    data.developerInternalID, 1, now, 0, downTime, data.released
                );
            } else {
                // 应用下架时只插入基本信息（不设置belongUs，保持数据库默认值）
                dbQuery = env.DB.prepare(`
                    INSERT INTO app_status (package_name, isAlive, lastCheck, downTime)
                    VALUES (?, ?, ?, ?)
                `).bind(app.package_name, 0, now, downTime);
            }
        }

        // 执行数据库操作
        await d1SafeRun(dbQuery, false);

        // 处理状态变化通知
        if (prevAlive !== currentStatus) {
            await sendStatusChangeAlert(
                env,
                app,
                currentStatus ? 'active' : 'taken_down',
                details,
                downTime
            );
        }
		// 检查是否是新应用（数据库无记录且应用在架）
		else if (!prevRecord && currentStatus) {
			await sendStatusChangeAlert(
				env,
				app,
				'new_active',
				details,
				null
			);
		}

    } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        console.error(`检查 ${app.app_name} 失败: ${errorMessage}`);

        // 静默更新最后检查时间
        try {
            const updateQuery = env.DB.prepare(`
                UPDATE app_status SET lastCheck = ? WHERE package_name = ?
            `).bind(now, app.package_name);
            await d1SafeRun(updateQuery, false);
        } catch (updateErr) {
            // 静默处理更新错误
        }
    }
}

function formatToBeiJingTime(isoString: string): string {
    const date = new Date(isoString);
    return new Date(date.getTime())
        .toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
}

async function sendStatusChangeAlert(
    env: Env,
    app: AppConfig,
    status: 'active' | 'taken_down' | 'new_active',
    details: PlayStoreResponse,
    downTime?: string | null
) {
    const action = status === 'active' ? '恢复上架' :
		status === 'taken_down' ? '下架' : '新上架';
    const emoji = status === 'active' ? '🎉' :
		status === 'taken_down' ? '❌' : '🆕';

    // 安全获取数据库记录
    let dbRecord: AppStatusRecord | null = null;
    try {
        const query = env.DB.prepare("SELECT * FROM app_status WHERE package_name = ?").bind(app.package_name);
        const result = await d1SafeRun(query);
        dbRecord = result.results?.[0] as AppStatusRecord || null;
    } catch (err) {
        console.error(`获取 ${app.package_name} 的数据库记录失败:`, err);
    }

    // 统一的安全字段获取函数
    const getField = (fieldName: string, maxLength: number, defaultValue: string = '未知') => {
        // 优先从数据库获取字段值（如果存在）
        if (dbRecord && (dbRecord as any)[fieldName]) {
            const value = (dbRecord as any)[fieldName];
            return typeof value === 'string'
                ? value.substring(0, maxLength)
                : value.toString().substring(0, maxLength);
        }

        // 其次从API响应中获取
        if (details && (details as any)[fieldName]) {
            const value = (details as any)[fieldName];
            return typeof value === 'string'
                ? value.substring(0, maxLength)
                : value.toString().substring(0, maxLength);
        }

        // 最后尝试从开发者对象获取
        if (details && details.developer && (details.developer as any)[fieldName]) {
            const value = (details.developer as any)[fieldName];
            return typeof value === 'string'
                ? value.substring(0, maxLength)
                : value.toString().substring(0, maxLength);
        }

        return defaultValue;
    };

    // 构建完整的开发者信息部分
    const buildDeveloperInfo = () => {
        let info = `开发者信息\n-------------\n`;

        // 获取所有可用的开发者字段
        const devId = getField('devId', 100, '未知');
        const legalName = getField('developerLegalName', 255, '未知');

        info += `• 开发者ID: ${devId}\n`;
        info += `• 主体名称: ${legalName}\n`;

        return info;
    };

    // 构建标题，包含状态信息
    const customTitle = `【安卓】${emoji} ${app.app_name} - ${action}`;

    // 消息内容不再包含状态信息（已在标题中）
    let message = `应用信息\n-------------\n`;
    message += `名称：${app.app_name}\n`;
    message += `包名：${app.package_name}\n`;

    // 添加应用标题（如果可用）
    const title = getField('title', 255, '');
    if (title !== '') {
        message += `Google Play标题：${title}\n`;
    }

    message += `\n`;

    // 添加开发者信息
    message += buildDeveloperInfo();

	// 时间信息
	const released = getField('released', 50, '未知');
	if (status !== 'new_active') {
    	message += `\n时间信息\n-------------\n`;

		if (status === 'taken_down' && released) {
			message += `上架时间：${released}\n`;
		} else if (status === 'active' && downTime) {
			message += `上架时间：${released}\n`;
			message += `上次下架：${formatToBeiJingTime(downTime)}\n`;
		}
	}

    // 添加Play Store链接（如果可用）
    const playStoreUrl = getField('playstoreUrl', 255, '');
    if (playStoreUrl !== '') {
        message += `\nPlay Store链接: ${playStoreUrl}\n`;
    }

    await sendLarkAlert(env, app, message, customTitle);
}

async function checkPlayStore(apiKey: string, packageName: string, retries: number = 2): Promise<PlayStoreResponse> {
    const url = `https://playapi.yuandao.world/api/apps/${packageName}?country=ph`;

    for (let attempt = 0; attempt <= retries; attempt++) {
        try {
            const res = await fetch(url, {
                method: 'GET',
                headers: { 'x-api-key': apiKey },
                // 添加超时控制
                signal: AbortSignal.timeout(10000) // 10秒超时
            });

            if (res.ok) {
                const data = await res.json();
                return {
                    status: 'available',
                    ...(data || {})
                };
            }

            if (res.status === 400) {
                return { status: 'unavailable' };
            }

            // 对于5xx错误进行重试
            if (res.status >= 500 && attempt < retries) {
                await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1))); // 递增延迟
                continue;
            }

            throw new Error(`API请求失败: ${res.status} ${res.statusText}`);

        } catch (error) {
            if (attempt === retries) {
                throw error;
            }
            // 网络错误重试
            await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
        }
    }

    throw new Error('API请求重试次数已用完');
}

async function sendLarkAlert(env: Env, app: AppConfig, message: string, customTitle?: string) {
    const webhookUrl = env.LARK_WEBHOOK_URL;

    // 使用自定义标题或默认标题
    const title = customTitle || `【安卓】${app.app_name} 状态更新`;

    // 构建富文本消息
    const requestBody = {
        msg_type: "post",
        content: {
            post: {
                zh_cn: {
                    title: title,
                    content: [
                        [
                            {
                                tag: "text",
                                text: message
                            }
                        ]
                    ]
                }
            }
        }
    };

    console.log('Lark request URL:', webhookUrl);
    console.log('Lark request body:', JSON.stringify(requestBody, null, 2));

    try {
        const response = await fetch(webhookUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody)
        });

        const responseText = await response.text();
        if (!response.ok) {
            throw new Error(`Lark API error: ${response.status} ${response.statusText}\nResponse: ${responseText}`);
        }

        const result = JSON.parse(responseText);
        console.log('Lark message sent successfully:', JSON.stringify(result, null, 2));
    } catch (error) {
        console.error('Failed to send Lark message:', error);
        throw error;
    }
}
