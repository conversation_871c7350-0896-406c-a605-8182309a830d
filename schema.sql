-- Play Store Monitor 数据库初始化脚本
-- 创建应用状态监控表

BEGIN TRANSACTION;

-- 创建应用状态表
CREATE TABLE IF NOT EXISTS app_status (
    package_name TEXT PRIMARY KEY NOT NULL,
    title TEXT,
    appId TEXT,
    devId TEXT,
    developerId TEXT,
    developerEmail TEXT,
    developerWebsite TEXT,
    developerLegalName TEXT,
    developerLegalEmail TEXT,
    developerLegalAddress TEXT,
    developerLegalPhoneNumber TEXT,
    developerInternalID TEXT,
    isAlive INTEGER NOT NULL,
    lastCheck TEXT NOT NULL,
    belongUs INTEGER NOT NULL DEFAULT 0,
    downTime TEXT,
    released TEXT
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_isAlive ON app_status (isAlive);
CREATE INDEX IF NOT EXISTS idx_lastCheck ON app_status (lastCheck);
CREATE INDEX IF NOT EXISTS idx_downTime ON app_status (downTime);
CREATE INDEX IF NOT EXISTS idx_belongUs ON app_status (belongUs);

COMMIT;
