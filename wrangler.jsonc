/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "play-store-monitor-id",
	"main": "src/index.ts",
	"compatibility_date": "2025-04-16",
	"observability":{
		"enabled": true
	},

	// 定时触发配置（必须使用UTC时间）
	"triggers": {
	  "crons": [
		"*/5 * * * *"  // 每5分钟执行
	  ]
	},

	// 添加路由
	"routes": [
	  {
		"pattern": "monitor-id.yuandao.world/*",
		"zone_name": "yuandao.world",
		"custom_domain": false
	  }
	],



	// KV存储配置（先创建命名空间）
	"kv_namespaces": [
	  {
		"binding": "APPS_STORE_ID_TMP",
		"id": "0c76d9c6fb774cfea2ea7cdd1526c379"          // 替换为实际ID
	  }
	],
	"d1_databases": [
	  {
		"binding": "DB",
		"database_name": "app_monitor_id", // 替换为实际数据库名称
		"database_id": "43994b10-c067-424c-9528-a9e5b4a29496"          // 替换为实际ID
	  }
	],

	// 环境变量（敏感数据应使用 secrets）

	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },

	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */

	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	// "vars": { "MY_VARIABLE": "production_value" },
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */

	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },

	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}
