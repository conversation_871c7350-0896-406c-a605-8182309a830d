/**
 * Cloudflare Workers 配置文件
 * Play Store Monitor - 应用状态监控系统
 *
 * 更多配置详情请参考:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "play-store-monitor-id",
	"main": "src/index.ts",
	"compatibility_date": "2025-04-10",

	// 启用可观测性功能（日志、指标等）
	"observability": {
		"enabled": true
	},

	// 定时触发配置 - 每5分钟执行一次监控任务（UTC时间）
	"triggers": {
		"crons": [
			"*/5 * * * *"
		]
	},

	// 路由配置 - 将自定义域名路由到 Worker
	"routes": [
		{
			"pattern": "monitor-id.yuandao.world/*",
			"zone_name": "yuandao.world",
			"custom_domain": false
		}
	],


	// KV 存储配置 - 用于存储应用配置信息
	// 使用命令创建: wrangler kv:namespace create "APPS_STORE_ID_TMP"
	"kv_namespaces": [
		{
			"binding": "APPS_STORE_ID_TMP",
			"id": "0c76d9c6fb774cfea2ea7cdd1526c379"  // 替换为实际的 KV 命名空间 ID
		}
	],

	// D1 数据库配置 - 用于存储应用状态和历史记录
	// 使用命令创建: wrangler d1 create app_monitor_id
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "app_monitor_id",
			"database_id": "43994b10-c067-424c-9528-a9e5b4a29496"  // 替换为实际的 D1 数据库 ID
		}
	],

	// 环境变量配置
	// 敏感数据（如 API 密钥）应使用 wrangler secret 命令设置
	// 开发环境可在 .dev.vars 文件中配置
	"vars": {
		"ENVIRONMENT": "production"
	},

	// 性能优化配置
	// Smart Placement: 自动选择最佳执行位置以减少延迟
	// "placement": { "mode": "smart" },

	// 资源限制配置
	"limits": {
		"cpu_ms": 30000  // CPU 时间限制 30 秒（适合监控任务）
	}

	// 其他可选配置（当前项目暂不需要）:
	//
	// 静态资源托管:
	// "assets": { "directory": "./public/", "binding": "ASSETS" }
	//
	// Service Bindings (Worker 间通信):
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
	//
	// 环境变量 (非敏感数据):
	// "vars": { "ENVIRONMENT": "production" }
}
