{"name": "play-store-monitor-id", "version": "1.0.0", "description": "Play Store 应用状态监控系统 - 基于 Cloudflare Workers", "private": true, "author": "zhoujunjie221", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/zhoujunjie221/play-store-monitor-id.git"}, "keywords": ["cloudflare-workers", "play-store", "monitoring", "app-status", "lark-notification"], "scripts": {"dev": "wrangler dev --test-scheduled", "start": "wrangler dev --test-scheduled", "deploy": "wrangler deploy", "deploy:prod": "wrangler deploy --env production", "cf-typegen": "wrangler types", "db:create": "wrangler d1 create app_monitor_id", "db:init": "wrangler d1 execute app_monitor_id --file=./schema.sql", "db:query": "wrangler d1 execute app_monitor_id --command", "kv:create": "wrangler kv:namespace create APPS_STORE_ID_TMP", "logs": "wrangler tail", "secret:set": "wrangler secret put"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250416.0", "typescript": "^5.5.2", "wrangler": "^4.9.1"}}