# Play Store Monitor - 应用状态监控系统

## 概述

Play Store Monitor 是一个基于 Cloudflare Workers 的应用状态监控系统，专门用于跟踪 Google Play Store 上应用的状态变化。系统会自动检测应用是否在架，并在状态变化时发送通知到飞书（Lark）。

## 主要功能

1. **自动监控**：每5分钟自动检查应用状态
2. **状态变化通知**：当应用下架或重新上架时发送飞书通知
3. **应用管理**：通过API添加/移除监控的应用
4. **历史记录**：存储应用的详细信息和状态变化历史
5. **错误处理**：自动重试和错误日志记录
6. **健康检查**：提供系统健康状态检查端点
7. **智能通知**：支持新应用上架、应用恢复上架等多种状态通知

## 技术栈

- **Cloudflare Workers**：无服务器运行环境
- **D1 Database**：Cloudflare的关系型数据库，存储应用详细信息
- **KV存储**：用于存储应用配置信息
- **飞书Webhook**：状态变化通知
- **TypeScript**：类型安全的开发体验

## 快速开始

### 前提条件

1. **Cloudflare 账户**：需要有 Workers 和 D1 数据库权限
2. **飞书群组**：用于接收通知，需要创建自定义机器人获取 Webhook URL
3. **Play Store API 访问密钥**：用于查询应用状态
4. **Node.js**：版本 18+
5. **Wrangler CLI**：Cloudflare 的命令行工具

### 安装步骤

```bash
# 1. 克隆仓库
git clone https://github.com/zhoujunjie221/play-store-monitor-id.git
cd play-store-monitor-id

# 2. 安装依赖
npm install

# 3. 安装 Wrangler CLI（如果未安装）
npm install -g wrangler

# 4. 登录 Cloudflare
wrangler auth login

# 5. 创建 D1 数据库
wrangler d1 create app_monitor_id

# 6. 创建 KV 命名空间
wrangler kv:namespace create "APPS_STORE_ID_TMP"

# 7. 更新 wrangler.jsonc 中的数据库和 KV ID

# 8. 初始化数据库表
wrangler d1 execute app_monitor_id --file=./schema.sql

# 9. 配置环境变量（创建 .dev.vars 文件）
echo "API_KEY=your_play_store_api_key" > .dev.vars
echo "MANUAL_TRIGGER_KEY=your_secure_trigger_key" >> .dev.vars
echo "LARK_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_token" >> .dev.vars

# 10. 本地测试
wrangler dev

# 11. 部署到生产环境
wrangler deploy
```

## API 端点

系统提供以下 REST API 端点，访问地址：`https://monitor-id.yuandao.world`

### 1. 健康检查
- **URL**: `/health`
- **方法**: GET
- **认证**: 无需认证
- **响应**:
  ```json
  {
    "status": "ok",
    "db": true
  }
  ```

### 2. 手动触发监控任务
- **URL**: `/trigger`
- **方法**: GET
- **认证**: Bearer Token
- **响应**: `监测任务已启动`
- **说明**: 立即执行一次完整的应用状态检查

### 3. 应用管理
- **URL**: `/manage`
- **认证**: Bearer Token
- **方法**:
  - **GET**: 获取所有跟踪应用列表
  - **POST**: 添加新应用到监控列表
  - **DELETE**: 从监控列表移除应用

#### 添加应用示例
```bash
curl -X POST https://monitor-id.yuandao.world/manage \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_secure_trigger_key" \
  -d '{
    "package_name": "com.example.app",
    "app_name": "Example App"
  }'
```

#### 删除应用示例
```bash
curl -X DELETE https://monitor-id.yuandao.world/manage \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_secure_trigger_key" \
  -d '{
    "package_name": "com.example.app"
  }'
```

### 4. 测试飞书通知
- **URL**: `/test-lark`
- **方法**: GET
- **认证**: Bearer Token
- **响应**: `Test message sent successfully`
- **说明**: 发送测试消息到飞书群组，验证通知功能

## 数据库结构

### `app_status` 表结构

| 字段名 | 类型 | 描述 | 备注 |
|--------|------|------|------|
| package_name | TEXT (PK) | 应用包名 | 主键，如 com.example.app |
| title | TEXT | 应用标题 | 应用在 Play Store 的显示名称 |
| appId | TEXT | 应用ID | Play Store 内部应用ID |
| devId | TEXT | 开发者ID | 开发者在 Play Store 的ID |
| developerId | TEXT | 开发者ID | 开发者标识符 |
| developerEmail | TEXT | 开发者邮箱 | 开发者联系邮箱 |
| developerWebsite | TEXT | 开发者网站 | 开发者官方网站 |
| developerLegalName | TEXT | 开发者法定名称 | 开发者的法定公司名称 |
| developerLegalEmail | TEXT | 开发者法定邮箱 | 法定联系邮箱 |
| developerLegalAddress | TEXT | 开发者法定地址 | 法定注册地址 |
| developerLegalPhoneNumber | TEXT | 开发者联系电话 | 法定联系电话 |
| developerInternalID | TEXT | 开发者内部ID | 内部开发者标识 |
| isAlive | INTEGER | 是否在架 | 1=在架，0=下架 |
| lastCheck | TEXT | 最后检查时间 | ISO 8601 格式时间戳 |
| belongUs | INTEGER | 是否属于我们 | 1=我们的应用，0=竞品应用 |
| downTime | TEXT | 下架时间 | 应用下架的时间戳 |
| released | TEXT | 发布时间 | 应用首次发布时间 |

### 索引

- `idx_isAlive`: 基于 isAlive 字段的索引，用于快速查询在架/下架应用
- `idx_lastCheck`: 基于 lastCheck 字段的索引，用于查询检查时间
- `idx_downTime`: 基于 downTime 字段的索引，用于查询下架时间
- `idx_belongUs`: 基于 belongUs 字段的索引，用于区分自有应用和竞品

## 定时任务

系统配置了每5分钟执行一次的自动监控任务：
```json
"triggers": {
  "crons": ["*/5 * * * *"]  // 每5分钟执行一次（UTC时间）
}
```

### 监控流程
1. 系统每5分钟自动触发监控任务
2. 从 KV 存储获取所有需要监控的应用列表
3. 逐个检查每个应用的 Play Store 状态
4. 对比数据库中的历史状态，检测状态变化
5. 如有状态变化，发送飞书通知并更新数据库
6. 记录检查时间和结果

## 环境变量配置

### 必需的环境变量

| 变量名 | 描述 | 示例 |
|--------|------|------|
| `API_KEY` | Play Store API 访问密钥 | `your_api_key_here` |
| `MANUAL_TRIGGER_KEY` | API 认证令牌 | `your_secure_random_key` |
| `LARK_WEBHOOK_URL` | 飞书机器人 Webhook URL | `https://open.feishu.cn/open-apis/bot/v2/hook/xxx` |

### 配置方法

**本地开发**：创建 `.dev.vars` 文件
```bash
API_KEY=your_play_store_api_key
MANUAL_TRIGGER_KEY=your_secure_trigger_key
LARK_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your_webhook_token
```

**生产环境**：使用 Wrangler secrets
```bash
wrangler secret put API_KEY
wrangler secret put MANUAL_TRIGGER_KEY
wrangler secret put LARK_WEBHOOK_URL
```

## 开发指南

### 本地开发
```bash
# 启动本地开发服务器（支持定时任务测试）
wrangler dev --test-scheduled

# 访问本地服务
# http://localhost:8787
```

### 测试
```bash
# 健康检查
curl http://localhost:8787/health

# 测试添加应用
curl -X POST http://localhost:8787/manage \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_secure_trigger_key" \
  -d '{"package_name":"com.example.app","app_name":"Example App"}'

# 手动触发监控
curl -H "Authorization: Bearer your_secure_trigger_key" \
  http://localhost:8787/trigger

# 测试飞书通知
curl -H "Authorization: Bearer your_secure_trigger_key" \
  http://localhost:8787/test-lark
```

### 生产部署
```bash
# 部署到生产环境
wrangler deploy

# 查看部署日志
wrangler tail

# 查看 D1 数据库内容
wrangler d1 execute app_monitor_id --command="SELECT * FROM app_status LIMIT 10"
```

## 项目结构

```
play-store-monitor-id/
├── src/
│   ├── index.ts          # 主要业务逻辑
│   └── types.ts          # TypeScript 类型定义
├── schema.sql            # 数据库初始化脚本
├── wrangler.jsonc        # Cloudflare Workers 配置
├── package.json          # 项目依赖和脚本
├── tsconfig.json         # TypeScript 配置
└── README.md            # 项目文档
```

## 监控逻辑

### 状态检测
- **在架检测**：通过 Play Store API 返回 200 状态码判断
- **下架检测**：API 返回 400 状态码表示应用不可用
- **错误处理**：5xx 错误会自动重试，网络错误也会重试

### 通知策略
- **新应用上架**：首次检测到应用在架时发送通知
- **应用下架**：检测到应用从在架变为下架时发送通知
- **应用恢复**：检测到应用从下架变为在架时发送通知
- **静默更新**：状态无变化时只更新检查时间，不发送通知

### 性能优化
- **并发控制**：防止重叠执行，使用全局锁机制
- **请求间隔**：每个应用检查间隔2秒，避免API限流
- **超时控制**：API 请求10秒超时，防止长时间阻塞
- **错误重试**：失败请求自动重试，递增延迟策略

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 D1 数据库 ID 是否正确配置
   - 确认数据库已正确初始化

2. **API 认证失败**
   - 检查 `API_KEY` 环境变量是否正确设置
   - 确认 Play Store API 密钥有效

3. **飞书通知失败**
   - 检查 `LARK_WEBHOOK_URL` 是否正确
   - 确认飞书机器人权限正常

4. **定时任务不执行**
   - 检查 Cloudflare Workers 的 Cron Triggers 是否启用
   - 查看 Workers 日志确认执行情况

### 调试命令

```bash
# 查看实时日志
wrangler tail

# 检查数据库状态
wrangler d1 execute app_monitor_id --command="SELECT COUNT(*) as total FROM app_status"

# 查看最近检查的应用
wrangler d1 execute app_monitor_id --command="SELECT package_name, isAlive, lastCheck FROM app_status ORDER BY lastCheck DESC LIMIT 5"

# 手动触发监控（用于调试）
curl -H "Authorization: Bearer your_key" https://monitor-id.yuandao.world/trigger
```

## 贡献指南

欢迎提交 issue 和 pull request：

1. Fork 仓库
2. 创建特性分支 (`git checkout -b feature/your-feature`)
3. 提交更改 (`git commit -am 'Add some feature'`)
4. 推送到分支 (`git push origin feature/your-feature`)
5. 创建 Pull Request

### 开发规范
- 使用 TypeScript 进行类型安全开发
- 遵循现有的代码风格和注释规范
- 添加适当的错误处理和日志记录
- 更新相关文档

## 许可证

本项目采用 MIT License。详见 [LICENSE](LICENSE) 文件。

## 更新日志

### v1.0.0
- ✅ 基础监控功能
- ✅ 飞书通知集成
- ✅ REST API 管理接口
- ✅ 自动定时任务
- ✅ 健康检查端点
- ✅ 错误重试机制
- ✅ 数据库索引优化
